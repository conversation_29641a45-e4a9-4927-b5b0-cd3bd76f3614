import { apiClient } from "../api/api-client";
import { 
  NavigationResponse, 
  GlobalPermissionsResponse, 
  NavigationItem,
  GlobalPermissions 
} from "../types/api";

/**
 * Cache for navigation and permissions data
 */
interface NavigationCache {
  navigation: {
    data: NavigationItem[] | null;
    timestamp: number | null;
  };
  permissions: {
    data: GlobalPermissions | null;
    timestamp: number | null;
  };
}

// In-memory cache
const cache: NavigationCache = {
  navigation: {
    data: null,
    timestamp: null,
  },
  permissions: {
    data: null,
    timestamp: null,
  },
};

// Cache timeout in milliseconds (5 minutes)
const CACHE_TIMEOUT = 5 * 60 * 1000;

/**
 * Service for handling navigation and global permissions API calls
 */
export const NavigationService = {
  /**
   * Check if cached data is still valid
   */
  isCacheValid: (timestamp: number | null): boolean => {
    if (!timestamp) return false;
    return Date.now() - timestamp < CACHE_TIMEOUT;
  },

  /**
   * Fetch navigation items from backend
   */
  getNavigationItems: async (): Promise<NavigationItem[]> => {
    try {
      // Check cache first
      if (
        cache.navigation.data && 
        NavigationService.isCacheValid(cache.navigation.timestamp)
      ) {
        console.log("🔄 Using cached navigation data");
        return cache.navigation.data;
      }

      console.log("🌐 Fetching navigation from backend");
      const response = await apiClient.get<NavigationResponse>("/navigation");
      
      // Update cache
      cache.navigation.data = response.data.items;
      cache.navigation.timestamp = Date.now();
      
      return response.data.items;
    } catch (error) {
      console.error("❌ Error fetching navigation items:", error);
      
      // Return cached data if available, even if expired
      if (cache.navigation.data) {
        console.log("⚠️ Using expired cached navigation data as fallback");
        return cache.navigation.data;
      }
      
      throw error;
    }
  },

  /**
   * Fetch global permissions from backend
   */
  getGlobalPermissions: async (): Promise<GlobalPermissions> => {
    try {
      // Check cache first
      if (
        cache.permissions.data && 
        NavigationService.isCacheValid(cache.permissions.timestamp)
      ) {
        console.log("🔄 Using cached permissions data");
        return cache.permissions.data;
      }

      console.log("🌐 Fetching global permissions from backend");
      const response = await apiClient.get<GlobalPermissionsResponse>("/permissions/global");
      
      // Update cache
      cache.permissions.data = response.data.permissions;
      cache.permissions.timestamp = Date.now();
      
      return response.data.permissions;
    } catch (error) {
      console.error("❌ Error fetching global permissions:", error);
      
      // Return cached data if available, even if expired
      if (cache.permissions.data) {
        console.log("⚠️ Using expired cached permissions data as fallback");
        return cache.permissions.data;
      }
      
      throw error;
    }
  },

  /**
   * Fetch both navigation and permissions in parallel
   */
  getNavigationAndPermissions: async (): Promise<{
    navigation: NavigationItem[];
    permissions: GlobalPermissions;
  }> => {
    try {
      const [navigation, permissions] = await Promise.all([
        NavigationService.getNavigationItems(),
        NavigationService.getGlobalPermissions(),
      ]);

      return { navigation, permissions };
    } catch (error) {
      console.error("❌ Error fetching navigation and permissions:", error);
      throw error;
    }
  },

  /**
   * Clear navigation cache
   */
  clearNavigationCache: (): void => {
    cache.navigation.data = null;
    cache.navigation.timestamp = null;
    console.log("🗑️ Navigation cache cleared");
  },

  /**
   * Clear permissions cache
   */
  clearPermissionsCache: (): void => {
    cache.permissions.data = null;
    cache.permissions.timestamp = null;
    console.log("🗑️ Permissions cache cleared");
  },

  /**
   * Clear all caches
   */
  clearAllCaches: (): void => {
    NavigationService.clearNavigationCache();
    NavigationService.clearPermissionsCache();
    console.log("🗑️ All navigation caches cleared");
  },

  /**
   * Force refresh navigation data
   */
  refreshNavigation: async (): Promise<NavigationItem[]> => {
    NavigationService.clearNavigationCache();
    return NavigationService.getNavigationItems();
  },

  /**
   * Force refresh permissions data
   */
  refreshPermissions: async (): Promise<GlobalPermissions> => {
    NavigationService.clearPermissionsCache();
    return NavigationService.getGlobalPermissions();
  },

  /**
   * Force refresh all data
   */
  refreshAll: async (): Promise<{
    navigation: NavigationItem[];
    permissions: GlobalPermissions;
  }> => {
    NavigationService.clearAllCaches();
    return NavigationService.getNavigationAndPermissions();
  },
};
