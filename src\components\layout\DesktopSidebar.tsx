import React from "react";
import { <PERSON> } from "react-router-dom";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LogoSection } from "./LogoSection";
import { NavItem } from "@/lib/types/navigation";
import { User } from "@/lib/types/auth";

interface DesktopSidebarProps {
  isOpen: boolean;
  user: User | null;
  navItems: NavItem[];
  onToggle: () => void;
}

/**
 * Component for rendering the desktop sidebar
 */
export const DesktopSidebar: React.FC<DesktopSidebarProps> = ({
  isOpen,
  user,
  navItems,
  onToggle,
}) => {
  return (
    <aside
      className={cn(
        "hidden border-r bg-background transition-all duration-300 lg:block",
        isOpen ? "w-64" : "w-[70px]"
      )}
    >
      <div className="sticky top-0 z-30 flex h-screen flex-col">
        <div className="flex h-28 items-center justify-between border-b px-4">
          <LogoSection
            variant="sidebar"
            userRole={user?.role}
            sidebarOpen={isOpen}
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="flex-shrink-0"
          >
            {isOpen ? (
              <ChevronLeft className="h-5 w-5" />
            ) : (
              <ChevronRight className="h-5 w-5" />
            )}
            <span className="sr-only">
              {isOpen ? "Collapse sidebar" : "Expand sidebar"}
            </span>
          </Button>
        </div>
        <div className="flex-1 overflow-auto py-4">
          <nav className="flex flex-col gap-1 px-2">
            {navItems.map((item) => (
              <Link
                key={item.id || item.name}
                to={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  item.active
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted",
                  !isOpen && "justify-center px-0"
                )}
                title={!isOpen ? item.name : undefined}
              >
                {item.icon}
                {isOpen && <span>{item.name}</span>}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </aside>
  );
};
