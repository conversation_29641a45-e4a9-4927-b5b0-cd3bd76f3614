# Centralized Permissions System

## Overview

The permission system has been modified to be centralized in the backend. Instead of checking permissions on the client-side, the backend now includes permission information for each entity in the API response, allowing for more granular and dynamic permission control.

## API Response Structure

The backend now includes a `permissions` object for each entity in the `content` array of the `getEntities` API response:

```json
{
  "content": [
    {
      "id": "entity-1",
      "name": "Example Entity",
      "description": "Entity description",
      // ... other entity properties
      "permissions": {
        "view": "enabled",
        "edit": "disabled",
        "delete": "unauthorized",
        "custom": "enabled"
      }
    }
  ],
  "page": {
    // ... pagination info
  }
}
```

## Permission Status Types

The system supports three permission statuses:

- **`"enabled"`**: The action is allowed and the button will be rendered and functional
- **`"disabled"`**: The action button will be rendered but disabled (grayed out)
- **`"unauthorized"`**: The action button will not be rendered at all

## Implementation Details

### Type Definitions

New types have been added to support the centralized permission system:

```typescript
// Permission status for individual actions
export type PermissionStatus = "unauthorized" | "enabled" | "disabled";

// Permissions object for entity actions
export interface EntityPermissions {
  [actionId: string]: PermissionStatus;
}

// Entity with permissions - extends any entity type with permissions
export interface EntityWithPermissions<T = Record<string, any>> extends T {
  permissions: EntityPermissions;
}
```

### Entity Service Changes

The `EntityService.getEntities` method has been updated to:

- Return type changed from `Promise<TableData<T>>` to `Promise<TableData<EntityWithPermissions<T>>>`
- This ensures proper type safety when working with entities that include permissions
- The service automatically passes through all permission data from the API response

### ActionsCellRenderer Changes

The `ActionsCellRenderer` component has been updated to:

1. Extract permissions from the row data: `rowData?.permissions ?? {}`
2. Check the permission status for each action using the action ID
3. Render actions based on the permission status:
   - `"unauthorized"`: Don't render the button
   - `"disabled"`: Render the button but set `disabled={true}`
   - `"enabled"`: Render the button normally
4. Fall back to client-side permission checking if no backend permission is provided

### Backward Compatibility

The system maintains backward compatibility:

- If no `permissions` object is provided in the entity data, the system falls back to the existing client-side permission checking
- Existing action configurations with `permissions` arrays still work as before
- The system gracefully handles missing permission data

## Usage Examples

### Backend API Response

```json
{
  "content": [
    {
      "id": "project-123",
      "name": "Sample Project",
      "status": "active",
      "permissions": {
        "view": "enabled",
        "edit": "enabled",
        "delete": "disabled",
        "application": "unauthorized"
      }
    }
  ]
}
```

### Action Configuration

Actions are still configured in the entity config registry as before:

```typescript
actions: [
  { id: "view", label: "View", icon: "eye", action: "view" },
  { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
  { id: "delete", label: "Delete", icon: "trash", action: "delete" },
  {
    id: "application",
    label: "Go to Application",
    icon: Command,
    action: "custom",
  },
];
```

### Rendered Result

Based on the example above:

- **View button**: Rendered and enabled
- **Edit button**: Rendered and enabled
- **Delete button**: Rendered but disabled (grayed out)
- **Application button**: Not rendered at all

## Benefits

1. **Centralized Control**: Permissions are managed entirely by the backend
2. **Dynamic Permissions**: Permissions can vary per entity instance
3. **Granular Control**: Three different permission states provide fine-grained control
4. **Security**: No client-side permission logic that could be bypassed
5. **Flexibility**: Backend can implement complex permission logic based on user roles, entity state, business rules, etc.

## Migration Guide

### For Backend Developers

1. Update your API endpoints to include the `permissions` object in each entity
2. Implement your permission logic in the backend
3. Return appropriate permission statuses for each action

### For Frontend Developers

No changes are required for existing code. The system is backward compatible and will automatically use the new permission system when available.

## Testing

To test the permission system:

1. Ensure your backend API returns entities with the `permissions` object
2. Verify that actions are rendered according to their permission status
3. Test that disabled actions cannot be clicked
4. Confirm that unauthorized actions are not visible
5. Verify fallback behavior when no permissions are provided
