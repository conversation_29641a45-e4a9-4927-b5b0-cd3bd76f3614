import { useState, useCallback, useMemo } from "react";
import { useTable } from "@/hooks/useTable";
import { useToast } from "@/hooks/use-toast";
import { ListPageConfig, PageConfig } from "@/lib/types/page-config";
import { EntityService } from "@/lib/services/entity-service";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { ErrorResponse } from "@/lib/types/api";

interface UseDynamicPageProps {
  config: PageConfig;
  dynamicPath: string;
  contextData?: Record<string, any>;
}

interface UseDynamicPageReturn {
  // Common properties
  isLoading: boolean;
  error: Error | null;

  // List page properties (only available if config.type === "list")
  tableData?: any[];
  pageCount?: number;
  totalCount?: number;
  pagination?: any;
  sorting?: any;
  handlePaginationChange?: (pagination: any) => void;
  handleSortingChange?: (sorting: any) => void;
  handleFilterChange?: (filter: any) => void;
  refreshData?: () => void;

  // Action handlers
  handleCreate: () => void;
  handleView: (id: string) => void;
  handleEdit: (id: string) => void;
  handleDelete: (id: string, dynamicPath?: string) => Promise<boolean>;

  // Form state
  showForm: boolean;
  setShowForm: (show: boolean) => void;
  currentEntityId: string | null;
  setCurrentEntityId: (id: string | null) => void;

  // Permissions
  hasRequiredPermissions: boolean;
}

/**
 * Custom hook for managing dynamic page state and actions
 */
export function useDynamicPage({
  config,
  dynamicPath,
  contextData = {},
}: UseDynamicPageProps): UseDynamicPageReturn {
  const { toast } = useToast();
  const { hasPermission, hasPagePermission } = useAuth();
  const navigate = useNavigate();

  // State for form visibility
  const [showForm, setShowForm] = useState(false);
  const [currentEntityId, setCurrentEntityId] = useState<string | null>(null);

  // Check if user has required permissions - memoize this calculation
  const hasRequiredPermissions = useMemo(() => {
    // First check legacy permissions (for backward compatibility)
    const hasLegacyPermissions =
      !config.permissions ||
      config.permissions.every((permission) =>
        hasPermission(permission as any)
      );

    // If legacy permissions fail, return false
    if (!hasLegacyPermissions) return false;

    // Check global page permissions if available
    const pageId = config.id;
    if (pageId) {
      // For list pages, check 'view' and 'getAll' permissions
      if (config.type === "list") {
        return (
          hasPagePermission(pageId, "view") &&
          hasPagePermission(pageId, "getAll")
        );
      }
      // For other page types, check 'view' permission
      return hasPagePermission(pageId, "view");
    }

    // Fallback to legacy permissions if no pageId
    return hasLegacyPermissions;
  }, [
    config.permissions,
    config.id,
    config.type,
    hasPermission,
    hasPagePermission,
  ]);

  // Create a stable fetchData function for useTable
  const fetchData = useCallback(
    async (pagination: any, sorting: any, filter: any) => {
      // Merge context filter parameters with table filter
      const mergedFilter = {
        ...filter,
        ...(contextData.filterParams || {}),
      };

      console.log("🔍 useDynamicPage fetchData Debug:", {
        contextData,
        filterParams: contextData.filterParams,
        mergedFilter,
        dynamicPath,
        endpoint: `${config.endpoints.list}${dynamicPath}`,
        entityName: config.entityName,
      });

      // Build the endpoint URL - dynamicPath will be empty if useQueryParams is true
      const endpoint = `${config.endpoints.list}${dynamicPath}`;

      return await EntityService.getEntities(
        config.entityName,
        endpoint,
        pagination,
        sorting,
        mergedFilter
      );
    },
    [config.entityName, contextData.filterParams, dynamicPath]
  );

  // Initialize table if this is a list page
  const tableHook = useTable({
    fetchData,
    initialPageSize: (config as ListPageConfig).defaultPageSize || 10,
    initialSorting: (config as ListPageConfig).defaultSorting || [],
  });

  // Handle create action
  const handleCreate = useCallback(() => {
    // Check create permission if pageId is available
    const pageId = config.id;
    if (pageId && !hasPagePermission(pageId, "create")) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to create new items.",
        variant: "destructive",
      });
      return;
    }

    setCurrentEntityId(null);
    setShowForm(true);
  }, [config.id, hasPagePermission, toast]);

  // Handle view action - navigate to details page
  const handleView = useCallback(
    (id: string) => {
      // Navigate to the details page for this entity
      // Use lowercase for URL path but preserve original case for the entity name
      navigate(`/projects/${config.entityName.toLowerCase()}/${id}`);
    },
    [navigate, config.entityName]
  );

  // Handle edit action - show form dialog
  const handleEdit = useCallback((id: string) => {
    setCurrentEntityId(id);
    setShowForm(true);
  }, []);

  // Handle delete action - memoized
  const handleDelete = useCallback(
    async (id: string, dynamicPath = "") => {
      try {
        const success = await EntityService.deleteEntity(
          config.entityName,
          `${config.endpoints.delete}${dynamicPath}`,
          id
        );

        if (success) {
          toast({
            title: "Success",
            description: `${config.entityName} deleted successfully.`,
          });

          // Refresh table data if available
          if (tableHook?.refreshData) {
            tableHook.refreshData();
          }

          return true;
        } else {
          toast({
            title: "Error",
            description: `Failed to delete ${config.entityName}.`,
            variant: "destructive",
          });

          return false;
        }
      } catch (error) {
        console.error(`Error deleting ${config.entityName}:`, error);

        const errorMessage =
          (error as ErrorResponse).details ?? "Unknown error occurred";

        toast({
          title: "Error",
          description: `Failed to delete ${config.entityName}. ${errorMessage}`,
          variant: "destructive",
        });

        return false;
      }
    },
    [config.entityName, toast, tableHook?.refreshData]
  );

  // Memoize the return object to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      // Common properties
      isLoading: tableHook?.isLoading || false,
      error: tableHook?.error || null,

      // List page properties
      tableData: tableHook?.data,
      pageCount: tableHook?.pageCount,
      totalCount: tableHook?.totalCount,
      pagination: tableHook?.pagination,
      sorting: tableHook?.sorting,
      handlePaginationChange: tableHook?.handlePaginationChange,
      handleSortingChange: tableHook?.handleSortingChange,
      handleFilterChange: tableHook?.handleFilterChange,
      refreshData: tableHook?.refreshData,

      // Action handlers
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,

      // Form state
      showForm,
      setShowForm,
      currentEntityId,
      setCurrentEntityId,

      // Permissions
      hasRequiredPermissions,
    }),
    [
      tableHook?.isLoading,
      tableHook?.error,
      tableHook?.data,
      tableHook?.pageCount,
      tableHook?.totalCount,
      tableHook?.pagination,
      tableHook?.sorting,
      tableHook?.handlePaginationChange,
      tableHook?.handleSortingChange,
      tableHook?.handleFilterChange,
      tableHook?.refreshData,
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,
      showForm,
      setShowForm,
      currentEntityId,
      setCurrentEntityId,
      hasRequiredPermissions,
    ]
  );

  return returnValue;
}
