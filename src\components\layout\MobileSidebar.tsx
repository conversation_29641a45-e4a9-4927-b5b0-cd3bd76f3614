import React from "react";
import { <PERSON> } from "react-router-dom";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LogoSection } from "./LogoSection";
import { NavItem } from "@/lib/types/navigation";
import { User } from "@/lib/types/auth";

interface MobileSidebarProps {
  isOpen: boolean;
  user: User | null;
  navItems: NavItem[];
  onClose: () => void;
}

/**
 * Component for rendering the mobile sidebar
 */
export const MobileSidebar: React.FC<MobileSidebarProps> = ({
  isOpen,
  user,
  navItems,
  onClose,
}) => {
  return (
    <div
      className={cn(
        "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-all duration-200 lg:hidden",
        isOpen ? "opacity-100" : "pointer-events-none opacity-0"
      )}
    >
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-full max-w-xs border-r bg-background p-6 shadow-lg transition-transform duration-200",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex items-center justify-between">
          <LogoSection
            variant="mobile"
            isAuthenticated={true}
            userRole={user?.role}
          />
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
            <span className="sr-only">Close menu</span>
          </Button>
        </div>
        <nav className="mt-8 flex flex-col gap-2">
          {navItems.map((item) => (
            <Link
              key={item.id || item.name}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                item.active
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              )}
              onClick={onClose}
            >
              {item.icon}
              <span>{item.name}</span>
            </Link>
          ))}
        </nav>
      </div>
    </div>
  );
};
