import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { 
  FileText, 
  Users, 
  Settings, 
  FolderOpen, 
  DollarSign, 
  BarChart3,
  Home,
  Shield,
  Database,
  Calendar,
  Mail,
  Bell
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { NavigationService } from "@/lib/services/navigation-service";
import { NavigationItem } from "@/lib/types/api";
import { NavItem, NavigationState, IconMapping } from "@/lib/types/navigation";

/**
 * Icon mapping for backend navigation items
 * Maps backend icon identifiers to React components
 */
const ICON_MAPPING: IconMapping = {
  "file-text": <FileText className="h-5 w-5" />,
  "users": <Users className="h-5 w-5" />,
  "settings": <Settings className="h-5 w-5" />,
  "folder-open": <FolderOpen className="h-5 w-5" />,
  "dollar-sign": <DollarSign className="h-5 w-5" />,
  "bar-chart-3": <BarChart3 className="h-5 w-5" />,
  "home": <Home className="h-5 w-5" />,
  "shield": <Shield className="h-5 w-5" />,
  "database": <Database className="h-5 w-5" />,
  "calendar": <Calendar className="h-5 w-5" />,
  "mail": <Mail className="h-5 w-5" />,
  "bell": <Bell className="h-5 w-5" />,
};

/**
 * Convert backend navigation item to frontend nav item
 */
const convertToNavItem = (
  item: NavigationItem, 
  currentPath: string, 
  hasPermission: boolean
): NavItem => ({
  id: item.id,
  name: item.name,
  href: item.href,
  icon: ICON_MAPPING[item.icon] || <FileText className="h-5 w-5" />, // Fallback icon
  active: currentPath.startsWith(item.href),
  order: item.order,
  parentId: item.parentId,
  hasPermission,
});

/**
 * Hook for backend-driven navigation with fallback to client-side navigation
 */
export function useBackendNavigation() {
  const location = useLocation();
  const { isAuthenticated, user, hasNavigationPermission, globalPermissions } = useAuth();
  
  const [navigationState, setNavigationState] = useState<NavigationState>({
    items: [],
    isLoading: true,
    error: null,
    lastFetched: null,
  });

  /**
   * Fetch navigation items from backend
   */
  const fetchNavigation = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setNavigationState({
        items: [],
        isLoading: false,
        error: null,
        lastFetched: null,
      });
      return;
    }

    setNavigationState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log("🔄 Fetching navigation from backend");
      const backendItems = await NavigationService.getNavigationItems();
      
      // Convert backend items to frontend nav items with permission checking
      const navItems = backendItems
        .map(item => {
          const hasPermission = hasNavigationPermission(item.id);
          return convertToNavItem(item, location.pathname, hasPermission);
        })
        .filter(item => item.hasPermission) // Only show items user has permission for
        .sort((a, b) => a.order - b.order); // Sort by order

      setNavigationState({
        items: navItems,
        isLoading: false,
        error: null,
        lastFetched: new Date(),
      });

      console.log("✅ Navigation loaded successfully:", navItems.length, "items");
    } catch (error) {
      console.error("❌ Error fetching navigation:", error);
      
      setNavigationState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to load navigation",
      }));
    }
  }, [isAuthenticated, user, hasNavigationPermission, location.pathname]);

  /**
   * Refresh navigation data
   */
  const refreshNavigation = useCallback(async () => {
    await fetchNavigation();
  }, [fetchNavigation]);

  // Fetch navigation when user or permissions change
  useEffect(() => {
    fetchNavigation();
  }, [fetchNavigation, globalPermissions]);

  // Memoized navigation items with active state updates
  const navItems = useMemo(() => {
    return navigationState.items.map(item => ({
      ...item,
      active: location.pathname.startsWith(item.href),
    }));
  }, [navigationState.items, location.pathname]);

  return {
    navItems,
    isLoading: navigationState.isLoading,
    error: navigationState.error,
    lastFetched: navigationState.lastFetched,
    refreshNavigation,
  };
}

/**
 * Hook that provides navigation with fallback strategy
 * Uses backend navigation if available, falls back to client-side navigation
 */
export function useNavigationWithFallback() {
  const backendNav = useBackendNavigation();
  
  // If backend navigation fails or is empty, we could implement fallback logic here
  // For now, we'll just return the backend navigation
  return backendNav;
}
